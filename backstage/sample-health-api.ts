// Sample health check API endpoint for Next.js
// This file should be placed at pages/api/health.ts or app/api/health/route.ts (depending on your Next.js version)

// For Pages Router (pages/api/health.ts):
import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    // Perform any health checks here (database connectivity, external services, etc.)
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || '3000',
      version: process.env.npm_package_version || '1.0.0'
    };

    res.status(200).json(healthStatus);
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

// For App Router (app/api/health/route.ts):
/*
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Perform any health checks here (database connectivity, external services, etc.)
  const healthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    port: process.env.PORT || '3000',
    version: process.env.npm_package_version || '1.0.0'
  };

  return NextResponse.json(healthStatus, { status: 200 });
}
*/
