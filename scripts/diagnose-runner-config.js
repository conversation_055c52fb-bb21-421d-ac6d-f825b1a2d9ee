#!/usr/bin/env node

/**
 * Runner Plugin Configuration Diagnostic Script
 *
 * This script helps diagnose issues with Runner Plugin configuration
 * by checking if the required files are accessible from GitHub repositories.
 */

const https = require('https');

// Configuration
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
const TEST_REPOSITORIES = [
  'https://github.com/InduwaraSMPN/Next.js-Blog-Application',
  'https://github.com/InduwaraSMPN/next.js-blog-sample'
];

async function checkUrl(url, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      headers: {}
    };
    
    if (token) {
      options.headers['Authorization'] = `token ${token}`;
    }
    
    https.get(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

function buildRawUrl(repoUrl, filePath = '.runner/config.yml', branch = 'main') {
  // Convert GitHub repo URL to raw file URL
  const match = repoUrl.match(/https:\/\/github\.com\/([^\/]+)\/([^\/]+)/);
  if (match) {
    const [, owner, repo] = match;
    return `https://raw.githubusercontent.com/${owner}/${repo}/${branch}/${filePath}`;
  }
  return null;
}

async function diagnoseRepository(repoUrl) {
  console.log(`\n🔍 Diagnosing repository: ${repoUrl}`);
  console.log('=' .repeat(60));
  
  // Check if .runner/config.yml exists
  const configUrl = buildRawUrl(repoUrl, '.runner/config.yml');
  console.log(`📄 Checking config file: ${configUrl}`);
  
  try {
    const result = await checkUrl(configUrl, GITHUB_TOKEN);
    if (result.status === 200) {
      console.log('✅ Configuration file found!');
      console.log('📋 Content preview:');
      console.log(result.data.substring(0, 300) + (result.data.length > 300 ? '...' : ''));
    } else if (result.status === 404) {
      console.log('❌ Configuration file not found (404)');
      console.log('💡 Solution: Create .runner/config.yml in the repository root');
    } else {
      console.log(`⚠️  Unexpected status: ${result.status}`);
    }
  } catch (error) {
    console.log(`❌ Error accessing file: ${error.message}`);
  }
  
  // Check if Dockerfile exists
  const dockerfileUrl = buildRawUrl(repoUrl, 'Dockerfile');
  console.log(`\n🐳 Checking Dockerfile: ${dockerfileUrl}`);
  
  try {
    const result = await checkUrl(dockerfileUrl, GITHUB_TOKEN);
    if (result.status === 200) {
      console.log('✅ Dockerfile found!');
    } else if (result.status === 404) {
      console.log('❌ Dockerfile not found (404)');
      console.log('💡 Solution: Create a Dockerfile in the repository root');
    } else {
      console.log(`⚠️  Unexpected status: ${result.status}`);
    }
  } catch (error) {
    console.log(`❌ Error accessing Dockerfile: ${error.message}`);
  }
  
  // Check if package.json exists (for Node.js projects)
  const packageUrl = buildRawUrl(repoUrl, 'package.json');
  console.log(`\n📦 Checking package.json: ${packageUrl}`);
  
  try {
    const result = await checkUrl(packageUrl, GITHUB_TOKEN);
    if (result.status === 200) {
      console.log('✅ package.json found!');
      const packageData = JSON.parse(result.data);
      console.log(`📋 Project: ${packageData.name || 'Unknown'}`);
      console.log(`📋 Scripts: ${Object.keys(packageData.scripts || {}).join(', ')}`);
    } else if (result.status === 404) {
      console.log('❌ package.json not found (404)');
    }
  } catch (error) {
    console.log(`❌ Error accessing package.json: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Runner Plugin Configuration Diagnostic Tool');
  console.log('=' .repeat(60));
  
  // Check GitHub token
  if (!GITHUB_TOKEN) {
    console.log('⚠️  Warning: GITHUB_TOKEN environment variable not set');
    console.log('💡 Some private repositories may not be accessible');
  } else {
    console.log('✅ GitHub token found');
  }
  
  // Diagnose each repository
  for (const repo of TEST_REPOSITORIES) {
    await diagnoseRepository(repo);
  }
  
  console.log('\n📋 Summary and Recommendations:');
  console.log('=' .repeat(60));
  console.log('1. Ensure .runner/config.yml exists in your repository root');
  console.log('2. Ensure Dockerfile exists in your repository root');
  console.log('3. Add a health check endpoint to your application');
  console.log('4. Verify GitHub token has access to your repositories');
  console.log('5. Check that the source-location annotation points to the correct repository');
  
  console.log('\n📚 For detailed setup instructions, see:');
  console.log('   - RUNNER_PLUGIN_TROUBLESHOOTING.md');
  console.log('   - sample-runner-config.yml');
  console.log('   - sample-dockerfile');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkUrl, buildRawUrl, diagnoseRepository };
