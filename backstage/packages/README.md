# The Packages Folder

This is where your own applications and centrally managed libraries live, each
in a separate folder of its own.

From the start there's an `app` folder (for the frontend) and a `backend` folder
(for the Node backend), but you can also add more modules in here that house
your core additions and adaptations, such as themes, common React component
libraries, utilities, and similar.
