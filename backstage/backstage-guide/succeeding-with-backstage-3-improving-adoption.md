---
title: "Succeeding with Backstage 3 Improving Adoption"
authors:
  - <PERSON>
---
This third installment of the "Succeeding with Backstage" series explores how you can improve the adoption of Backstage within your organization. The previous two parts dealt with [customizing the look and feel of Backstage](URL) and [creating and maintaining custom plugins](URL).

As your organization continues its Backstage journey, it will become clear that successful implementation goes beyond the technical aspects of installation and operation. It's an ongoing process that demands careful planning, dedicated resources, active promotion, and continuous monitoring of usage and adoption. For Backstage to truly become the central hub where your developers do most of their work, you'll need to build a collaborative culture and find effective ways to "market" your platform internally.

In this guide, you'll explore key strategies and best practices for improving Backstage adoption and ensuring its long-term success as an integral part of your development experience.

## Guide Developers to Unlock the Value of Backstage

Backstage is all about improving the developer experience in your company, so developer buy-in is essential.

Instead of overwhelming developers with a one-off introductory demo and then leaving them to figure out how Backstage works on their own, consider implementing a systematic approach. Start by showcasing the [core features](https://www.kosli.com/blog/implementing-backstage-using-the-core-features/), such as the software catalog, and then demonstrate how some [additional plugins](URL) work. Next, dig deeper into how Backstage configuration files work and how annotations, which can be added to the [YAML definition](https://backstage.io/docs/features/software-catalog/well-known-annotations) attached to a component, can unlock new Backstage capabilities, such as adding documentation, Kubernetes dashboards, code quality tracking, and so on.

As developers unlock additional features, their engagement with the platform will increase and they’ll have more reason to return to it because the features will be helping them on a day-to-day basis. This gamifies the developer experience. If all features are "unlocked" by default and magically configured, your developers will lose the benefits of [discovery learning](https://lmshero.com/discovery-learning/). Instead, with the discovery learning approach, developers gain awareness and understanding of each feature they unlock. Later, you can establish a checklist to track the onboarding process, measuring the percentage of unlocked features as a gauge of progress.

To facilitate learning, consider providing onboarding paths tailored to different features. You can explore Backstage learning paths for ideas about how such documentation would look:

- [Onboarding Software to Backstage](https://backstage.spotify.com/learn/onboarding-software-to-backstage/)
- [Backstage for All](https://backstage.spotify.com/learn/backstage-for-all/)

Both of these are quite basic, but they still provide solid examples of how to document and present learning paths.

## Build a Team Dedicated to Creating and Maintaining Custom Plugins

Backstage is a highly customizable platform, and to get the most out of it you need to really engage with the code, install and configure plugins, customize the UI, and even write your own custom plugins. Expecting this to be a shared responsibility across your organization isn’t practical.

To drive Backstage adoption, you should build a dedicated team focused on your internal platform composed of Backstage and all the other tools and workflows you have adopted over the years. This dedicated team should operate Backstage, create and maintain custom plugins that align with your organization's specific needs and workflows, and serve as the backbone of Backstage adoption. For those leaning toward a platform engineering model, managing Backstage and related tools would be one of the responsibilities assigned to the team supporting the platform initiative.

Once your core Backstage team has laid the foundation, welcoming internal contributors (inner sourcing) into custom plugin development should become much easier. However, not every developer will be able to contribute. Backstage requires both TypeScript and React knowledge, so the dedicated team will need that set of skills.

In addition to your internal efforts, consider making relevant plugins open source and leveraging the broader Backstage community to enhance and help maintain your custom plugins over time. The Backstage community is valuable for collaborating on plugin development; it can introduce additional features on top of what you have already built.

Finally, this team's role is to ensure that your adoption efforts are on the right track and to closely monitor Backstage usage and adoption through internal key performance indicators (KPIs). As mentioned, you can use a checklist to track the onboarding process, but you should also identify how often developers visit Pages and the core interactions with Backstage. The [Google Analytics plugin](https://github.com/backstage/backstage/blob/master/plugins/analytics-module-ga/README.md) is a must-have. Your teams should also consider the following two Backstage premium plugins (made by Spotify) that help gather feedback and measure maturity and adoption:

* The [Pulse plugin](https://backstage.spotify.com/plugins/pulse/) for internal surveys
* The [Soundcheck plugin](https://backstage.spotify.com/plugins/soundcheck/) to track the maturity of Backstage components as well as their adoption

## Marketing Backstage Internally

Consider **mixing and merging teams** with people who have worked with Backstage to help accelerate awareness and adoption. The book [*Dynamic Reteaming*](https://www.heidihelfand.com/dynamic-reteaming/) by Heidi Helfand is a great source of inspiration for implementing such initiatives successfully. Helfand recommends a more fluid approach to team organization, which involves switching team members or merging teams (referred to as the "merge-and-switch" approach) so that team members with varying experience levels work together. Encourage knowledge sharing and mentorship among your teams—in this case, during the Backstage adoption process. Experienced team members can help onboard newcomers and share best practices.

This merge-and-switch team approach promotes knowledge sharing, facilitates mentorship, and helps spread Backstage awareness organically within teams.

**Cross-pollination** is another powerful approach that emphasizes the exchange of ideas, perspectives, and practices among different teams or groups, especially concerning transformation and innovation. In the context of Backstage adoption, consider working with a small pilot group that has successfully adopted Backstage or has a strong interest in adopting it. Have this group conduct company-wide demos, showcasing their achievements and Backstage's impact on their workflows. You can structure these demos using the [Tell, Show, Do, and Review](https://blogs.articulate.com/rapid-elearning/tell-show-plus-practice-review-build-effective-online-training/) method, which is particularly effective for training programs focused on task competency and skill development. As the name suggests, it consists of four steps. First, "tell" the audience what you achieved and why it matters, then "show" them what you did and how. Next, invite them to "do" the same thing in an example scenario (for instance, register components or update Backstage configuration to activate new features). Finally, "review" what they did, answer any questions, and invite them to apply that knowledge to the application they are responsible for.

The cross-pollination approach might be less disruptive than the merge-and-switch team approach in the early adoption stages, where your dedicated Backstage team still needs to polish the configuration and create the required custom plugins.

Your team may need help handling all the requests from developers to adapt Backstage to their needs. In such cases, **organizing hackathons or game days** dedicated to Backstage customization and plugin development is a great opportunity to expose more developers to the inner workings of Backstage and reduce the load on your dedicated team. These events provide an opportunity for teams to collaborate and work on specific problems, leading to innovative solutions. This is a win-win initiative; developers participating in the event gain a better understanding of the tools they use and are more likely to become advocates for Backstage and the features they contributed to building.

This approach helps break down the barrier between the development team and the platform engineering team, signaling to developers that the platform is theirs and they can improve it if they want to.

## Focus on the Onboarding Experience

Backstage adoption is an ongoing process as your organization experiences a continuous flow of employees joining and leaving.

It's essential that new team members get a proper introduction to Backstage. They need to know how to access information related to your company's ongoing projects, identify the responsible teams, and establish communication channels. Additionally, you should also demonstrate how Backstage can launch a new project with a [software template](https://backstage.io/docs/features/software-templates/).

Needless to say, your onboarding process needs to be kept up-to-date and continuously refined. This can be done by continuing, and building on, all the adoption activities discussed earlier:

- Tailor onboarding paths to different features
- Incorporate demos following the Tell, Show, Do, and Review format
- Ensure ongoing maintenance and improvement of internal Backstage documentation

The initial adoption effort is significant, but it's equally important to provide ongoing opportunities for developers to learn and engage, particularly for new team members who didn't go through the initial adoption phase.

To keep engagement high, use recurring discussion formats. For example, you can host fireside chats and organized talks based on Backstage.

Fireside chats typically involve a relaxed conversation where participants share insights, personal experiences, and anecdotes related to using a tool like Backstage. The [Lean Coffee](https://www.range.co/blog/lean-coffee) approach is a great facilitation technique to drive this kind of meeting. It helps you to gather up and then select conversation topics, ensuring dedicated time for the topics that gather the most interest. Such exercises also provide excellent opportunities for new employees to socialize within the company.

Organized talks about Backstage, both internal and external (at conferences and meetups), are a great way to demonstrate your achievements and keep your community updated. If you have a dedicated Backstage team, you'll always have subject matter experts available to lead these discussions. These talks should tell the story of your adoption journey so that people who joined after the adoption phase can relate to and understand the how and why of Backstage.

Your onboarding process plays a vital role in the internal marketing of Backstage. Fireside chats and organized talks also contribute to the cross-pollination approach. As Backstage becomes integrated into most teams, the traditional merge-and-switch team method may lose relevance. However, Heidi Helfand's [*Dynamic Reteaming*](https://www.heidihelfand.com/dynamic-reteaming/) introduces an interesting alternative approach called the "Ropes program," a bootcamp-style method used by the team at AppFolio. This is an onboarding strategy where new employees participate in pairing sessions with members from various teams, allowing them to learn how the company works while cultivating an internal social network.

## Conclusion

Getting your developers on board with Backstage's features is essential to ensure long-term success. You can do this by taking a systematic approach and introducing them to the features. It's important to have a dedicated team that tailors Backstage to your organization's needs, as Backstage adoption requires time and coding skills.

You can achieve better adoption with approaches like mix-and-merge teams, promoting cross-pollination via demos and learning activities, and hosting hackathons and games to spark innovation and bridge the gap between developers and platform engineers. But, don't forget to monitor Backstage usage through key performance indicators to gauge progress effectively. Finally, remember that adoption is an ongoing process that involves continuously onboarding new members. You must ensure new employees have all the resources and support they need to use Backstage effectively.

The next part of the series looks at how you can [improve Backstage’s success by incorporating it as part of a broader developer productivity engineering (DPE) initiative](URL).
