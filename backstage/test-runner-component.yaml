apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: test-runner-component
  description: A test component for the Runner Plugin
  annotations:
    # GitHub source location - using existing repo with content subdirectory
    backstage.io/source-location: https://github.com/InduwaraSMPN/next.js-blog-sample/tree/master/content
    # Runner Plugin annotations
    runner.backstage.io/enabled: "true"
    runner.backstage.io/config-path: ".runner/config.yml"
    runner.backstage.io/type: "docker"
  tags:
    - test
    - runner-enabled
    - docker
spec:
  type: website
  lifecycle: experimental
  owner: integrations-team
