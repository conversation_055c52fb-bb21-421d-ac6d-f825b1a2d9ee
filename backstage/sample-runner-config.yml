# Sample Runner Plugin Configuration for Next.js Blog Application
# This file should be placed at .runner/config.yml in your repository root

runner:
  type: docker
  dockerfile: ./Dockerfile
  ports: [3000]
  environment:
    NODE_ENV: production
    PORT: "3000"
    HOSTNAME: "0.0.0.0"
  healthCheck:
    path: /api/health
    interval: 30s
    timeout: 10s
  build:
    context: .
    args:
      BUILD_ENV: production

# Instructions:
# 1. Copy this file to .runner/config.yml in your repository
# 2. Ensure you have a Dockerfile in your repository root
# 3. Add a health check endpoint at /api/health in your application
# 4. Update the port number if your application uses a different port
# 5. Modify environment variables as needed for your application
