# Runner Plugin Troubleshooting Guide

## Error: "Git operation failed. Please check the repository URL and try again."

### Problem Description
This error occurs when the Runner Plugin cannot find the required `.runner/config.yml` file in the specified repository. The error typically shows:

```
Failed to read runner configuration: NotFoundError: could not read https://raw.githubusercontent.com/[owner]/[repo]/main/.runner/config.yml, 404 Not Found
```

### Root Cause
The component's source repository is missing the required Runner Plugin configuration file at `.runner/config.yml`.

### Solution Steps

#### 1. Create the Runner Configuration File
Create a file named `config.yml` in a `.runner` directory at the root of your repository:

```yaml
# .runner/config.yml
runner:
  type: docker
  dockerfile: ./Dockerfile
  ports: [3000]
  environment:
    NODE_ENV: production
    PORT: "3000"
    HOSTNAME: "0.0.0.0"
  healthCheck:
    path: /api/health
    interval: 30s
    timeout: 10s
  build:
    context: .
    args:
      BUILD_ENV: production
```

#### 2. Create a Dockerfile
Ensure your repository has a `Dockerfile` at the root. For Next.js applications, use a multi-stage build:

```dockerfile
FROM node:18-alpine AS base
# ... (see sample-dockerfile for complete example)
```

#### 3. Add Health Check Endpoint
Create a health check API endpoint in your application:

**For Next.js Pages Router** (`pages/api/health.ts`):
```typescript
import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
```

#### 4. Update Component Annotations
Ensure your `catalog-info.yaml` has the correct annotations:

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: your-component-name
  annotations:
    backstage.io/source-location: https://github.com/owner/repo/tree/main
    runner.backstage.io/enabled: "true"
    runner.backstage.io/config-path: ".runner/config.yml"
    runner.backstage.io/type: "docker"
spec:
  type: website
  lifecycle: experimental
  owner: your-team
```

#### 5. Verify GitHub Integration
Ensure your Backstage instance has proper GitHub integration configured in `app-config.yaml`:

```yaml
integrations:
  github:
    - host: github.com
      token: ${GITHUB_TOKEN}

backend:
  reading:
    allow:
      - host: raw.githubusercontent.com
      - host: github.com
      - host: '*.github.com'
```

### Common Issues and Solutions

1. **Wrong branch reference**: Ensure the source location points to the correct branch (usually `main` or `master`)
2. **Missing GitHub token**: Verify the `GITHUB_TOKEN` environment variable is set
3. **Private repository access**: Ensure the GitHub token has access to private repositories if needed
4. **File path case sensitivity**: Ensure `.runner/config.yml` uses the exact case
5. **YAML syntax errors**: Validate your YAML syntax using a YAML validator

### Testing the Configuration

1. Verify the configuration file is accessible:
   ```bash
   curl https://raw.githubusercontent.com/[owner]/[repo]/main/.runner/config.yml
   ```

2. Test the health endpoint locally:
   ```bash
   curl http://localhost:3000/api/health
   ```

3. Build and test the Docker image:
   ```bash
   docker build -t test-app .
   docker run -p 3000:3000 test-app
   ```

### Enhanced Error Handling

The Runner Plugin now provides more specific error messages:
- `CONFIG_FILE_NOT_FOUND`: The `.runner/config.yml` file is missing
- `INVALID_CONFIGURATION`: The configuration file has syntax or validation errors
- `GIT_OPERATION_FAILED`: General Git operation failures

For additional help, check the Backstage logs for detailed error information.
