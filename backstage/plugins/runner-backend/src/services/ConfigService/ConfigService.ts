import { UrlReaderService } from '@backstage/backend-plugin-api';
import { Entity } from '@backstage/catalog-model';
import { RunnerConfig } from '../RunnerService/types';
import * as yaml from 'yaml';

export class ConfigService {
  constructor(private urlReader: UrlReaderService) {}

  async getRunnerConfig(entity: Entity): Promise<RunnerConfig> {
    const configPath = entity.metadata.annotations?.['runner.backstage.io/config-path'] || '.runner/config.yml';
    const sourceLocation = entity.metadata.annotations?.['backstage.io/source-location'];

    if (!sourceLocation) {
      throw new Error('Component missing source location annotation');
    }

    // Convert GitHub blob URL to raw URL for file reading
    const configUrl = this.buildRawFileUrl(sourceLocation, configPath);

    try {
      const response = await this.urlReader.readUrl(configUrl);
      const configContent = await response.buffer();
      const config = yaml.parse(configContent.toString());

      return this.validateConfig(config.runner);
    } catch (error) {
      // Provide more detailed error information
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
        throw new Error(`NotFoundError: could not read ${configUrl}, 404 Not Found`);
      }

      if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
        throw new Error(`Access denied: could not read ${configUrl}, 403 Forbidden. Check repository permissions and GitHub token.`);
      }

      throw new Error(`Failed to read runner configuration: ${errorMessage}`);
    }
  }

  private buildRawFileUrl(sourceLocation: string, configPath: string): string {
    // Handle GitHub URLs - convert from blob/tree URLs to raw URLs
    if (sourceLocation.includes('github.com')) {
      // Remove trailing slashes and normalize
      const cleanSourceLocation = sourceLocation.replace(/\/$/, '');

      // Extract parts from GitHub URL including subdirectories
      // Expected format: https://github.com/owner/repo/tree/branch/subpath or https://github.com/owner/repo/blob/branch/subpath
      const match = cleanSourceLocation.match(/https:\/\/github\.com\/([^\/]+)\/([^\/]+)(?:\/(?:tree|blob)\/([^\/]+)(?:\/(.+))?)?/);

      if (match) {
        const [, owner, repo, branch = 'main', subPath = ''] = match;
        // Construct the full path including subdirectory if present
        const fullPath = subPath ? `${subPath}/${configPath}` : configPath;
        // Construct raw GitHub URL
        return `https://raw.githubusercontent.com/${owner}/${repo}/${branch}/${fullPath}`;
      }
    }

    // Fallback for other sources or if parsing fails
    // Remove any existing /blob/main or /tree/main and add the config path
    const baseUrl = sourceLocation
      .replace(/\/blob\/[^\/]+\/?$/, '')
      .replace(/\/tree\/[^\/]+\/?$/, '');

    return `${baseUrl}/blob/main/${configPath}`;
  }

  private validateConfig(config: any): RunnerConfig {
    if (!config || config.type !== 'docker') {
      throw new Error('Invalid runner configuration: type must be "docker"');
    }

    if (!config.dockerfile) {
      throw new Error('Invalid runner configuration: dockerfile is required');
    }

    if (!config.ports || !Array.isArray(config.ports) || config.ports.length === 0) {
      throw new Error('Invalid runner configuration: ports array is required');
    }

    return {
      type: 'docker',
      dockerfile: config.dockerfile,
      ports: config.ports,
      environment: config.environment || {},
      healthCheck: config.healthCheck,
      build: config.build || { context: '.' }
    };
  }
}
